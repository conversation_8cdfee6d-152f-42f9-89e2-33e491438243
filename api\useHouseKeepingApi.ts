import useClient from './useClient';
import {
  HKAddJobResponse,
  HKChecklist,
  HKExtendedJob,
  HKJob,
  HKJobSection,
  HKProperty,
  HKSection,
  HKTask,
} from '~/types';

const useHouseKeepingApi = () => {
  const client = useClient();

  const addJob = async ({
    job: { id, leaderPerforms, leaderName, helperName, performType },
    propertyId,
    taskAssignmentId,
  }: {
    job: HKJob;
    propertyId: HKProperty['id'];
    taskAssignmentId: number;
  }) => {
    const apiJob = {
      jobId: id,
      leaderPerforms,
      leaderName,
      helperName,
      performType,
      taskAssignmentId,
      propertyId,
    };
    const newJob = await client
      .post('/checklist/jobs', apiJob)
      .then(res => res.data as HKAddJobResponse);
    return newJob;
  };

  const startChecklist = async ({
    jobId,
    checklist,
    checklistToken,
  }: {
    jobId: HKJob['id'];
    checklist: HKChecklist;
    checklistToken?: string;
  }) => {
    const startedJob = await client
      .post(`/checklist/jobs/${jobId}/start`, {
        checklist,
        checklistToken,
      })
      .then(res => res.data as HKExtendedJob);
    return startedJob;
  };

  const completeJobSection = async ({
    jobSection: {
      id,
      items,
      location: ,
    },
    checklistToken,
  }: {
    jobSection: HKJobSection;
    checklistToken?: string;
  }) => {
    const apiJobSection = {
      id: jobSection.id,
      items: jobSection.items,
      location: {
        coordinates: jobSection.location?.coordinates,
        address: jobSection.location?.address,
        timezone: jobSection.location?.timezone,
      },
      checklistToken,
    };

    const result = await client
      .post('/checklist/complete-section', apiJobSection)
      .then(res => res.data as HKExtendedJob);

    return result;
  };

  const fetchJob = async (jobId: HKJob['id'], checklistToken?: string) => {
    const job = await client
      .get(`/checklist/jobs/${jobId}`, {
        params: {
          checklistToken,
        },
      })
      .then(res => res.data as HKExtendedJob);
    return job;
  };

  const fetchItems = async (checklistToken?: string) => {
    const items = await client
      .get('/checklist/items', {
        params: {
          checklistToken,
        },
      })
      .then(res => res.data as HKTask[]);
    return items;
  };

  const fetchSections = async (checklistToken?: string) => {
    const sections = await client
      .get('/checklist/sections', {
        params: {
          checklistToken,
        },
      })
      .then(res => res.data as HKSection[]);
    return sections;
  };

  const finishJob = async ({
    jobId,
    checklistToken,
  }: {
    jobId: HKJob['id'];
    checklistToken?: string;
  }) => {
    const result = await client
      .post(`/checklist/jobs/${jobId}/finish`, {
        checklistToken,
      })
      .then(res => res.data as HKExtendedJob);
    return result;
  };

  return {
    addJob,
    startChecklist,
    completeJobSection,
    fetchJob,
    fetchSections,
    fetchItems,
    finishJob,
  };
};

export default useHouseKeepingApi;
